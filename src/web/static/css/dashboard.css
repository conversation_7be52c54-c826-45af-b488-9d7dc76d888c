/* 套利交易系统仪表板样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header p {
    text-align: center;
    font-size: 1.1em;
    opacity: 0.9;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.status-card h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.status-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #333;
}

.status-positive {
    color: #28a745;
}

.status-negative {
    color: #dc3545;
}

.status-warning {
    color: #ffc107;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
    color: #667eea;
    margin-bottom: 15px;
    text-align: center;
}

.trades-table {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.trades-table h3 {
    color: #667eea;
    margin-bottom: 15px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #667eea;
}

tr:hover {
    background-color: #f5f5f5;
}

.profit {
    color: #28a745;
    font-weight: bold;
}

.loss {
    color: #dc3545;
    font-weight: bold;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.refresh-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    margin-bottom: 20px;
}

.refresh-btn:hover {
    background: #5a6fd8;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .container {
        padding: 10px;
    }
}

/* 动画效果 */
.status-card, .chart-container, .trades-table {
    transition: transform 0.2s ease-in-out;
}

.status-card:hover, .chart-container:hover {
    transform: translateY(-2px);
}

/* 加载动画 */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
