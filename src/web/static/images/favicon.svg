<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="50" cy="50" r="45" fill="url(#grad1)" stroke="#fff" stroke-width="2"/>
  
  <!-- 套利符号 - 双向箭头 -->
  <g fill="#fff" stroke="#fff" stroke-width="2">
    <!-- 上箭头 -->
    <path d="M30 35 L50 20 L70 35" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M45 25 L50 20 L55 25" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- 下箭头 -->
    <path d="M30 65 L50 80 L70 65" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M45 75 L50 80 L55 75" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- 中间的美元符号 -->
    <text x="50" y="55" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#fff">$</text>
  </g>
</svg>
