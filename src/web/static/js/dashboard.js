// 套利交易系统仪表板JavaScript

class ArbitrageDashboard {
    constructor() {
        this.refreshInterval = 5000; // 5秒刷新一次
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // 窗口大小变化时重新调整图表
        window.addEventListener('resize', () => {
            this.resizeCharts();
        });
    }

    async loadInitialData() {
        try {
            await this.loadSystemStatus();
            await this.loadTradeHistory();
            await this.loadCharts();
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showAlert('数据加载失败，请检查网络连接', 'danger');
        }
    }

    async loadSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            this.updateStatusCards(data);
        } catch (error) {
            console.error('加载系统状态失败:', error);
        }
    }

    updateStatusCards(data) {
        // 更新系统状态卡片
        const elements = {
            'total-profit': data.total_profit || 0,
            'active-trades': data.active_trades || 0,
            'success-rate': data.success_rate || 0,
            'system-status': data.system_status || '未知'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (typeof value === 'number') {
                    element.textContent = this.formatNumber(value);
                    this.updateStatusClass(element, value);
                } else {
                    element.textContent = value;
                }
            }
        });
    }

    updateStatusClass(element, value) {
        // 移除现有的状态类
        element.classList.remove('status-positive', 'status-negative', 'status-warning');
        
        // 根据值添加相应的状态类
        if (value > 0) {
            element.classList.add('status-positive');
        } else if (value < 0) {
            element.classList.add('status-negative');
        } else {
            element.classList.add('status-warning');
        }
    }

    async loadTradeHistory() {
        try {
            const response = await fetch('/api/trades');
            const data = await response.json();
            
            this.updateTradeTable(data.trades || []);
        } catch (error) {
            console.error('加载交易历史失败:', error);
        }
    }

    updateTradeTable(trades) {
        const tbody = document.querySelector('#trades-table tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (trades.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无交易记录</td></tr>';
            return;
        }

        trades.forEach(trade => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.formatDate(trade.timestamp)}</td>
                <td>${trade.symbol}</td>
                <td>${trade.type}</td>
                <td>$${this.formatNumber(trade.amount)}</td>
                <td class="${trade.profit >= 0 ? 'profit' : 'loss'}">
                    $${this.formatNumber(trade.profit)}
                </td>
                <td>${trade.status}</td>
            `;
            tbody.appendChild(row);
        });
    }

    async loadCharts() {
        try {
            await this.loadProfitChart();
            await this.loadVolumeChart();
        } catch (error) {
            console.error('加载图表失败:', error);
        }
    }

    async loadProfitChart() {
        try {
            const response = await fetch('/api/profit-chart');
            const data = await response.json();
            
            // 这里可以集成Chart.js或其他图表库
            this.renderProfitChart(data);
        } catch (error) {
            console.error('加载利润图表失败:', error);
        }
    }

    async loadVolumeChart() {
        try {
            const response = await fetch('/api/volume-chart');
            const data = await response.json();
            
            this.renderVolumeChart(data);
        } catch (error) {
            console.error('加载交易量图表失败:', error);
        }
    }

    renderProfitChart(data) {
        const canvas = document.getElementById('profit-chart');
        if (!canvas) return;

        // 简单的文本显示，实际项目中可以使用Chart.js
        const container = canvas.parentElement;
        container.innerHTML = `
            <h3>利润趋势</h3>
            <div class="chart-placeholder">
                <p>总利润: $${this.formatNumber(data.total_profit || 0)}</p>
                <p>今日利润: $${this.formatNumber(data.today_profit || 0)}</p>
                <p>本周利润: $${this.formatNumber(data.week_profit || 0)}</p>
            </div>
        `;
    }

    renderVolumeChart(data) {
        const canvas = document.getElementById('volume-chart');
        if (!canvas) return;

        const container = canvas.parentElement;
        container.innerHTML = `
            <h3>交易量统计</h3>
            <div class="chart-placeholder">
                <p>总交易量: $${this.formatNumber(data.total_volume || 0)}</p>
                <p>今日交易量: $${this.formatNumber(data.today_volume || 0)}</p>
                <p>交易次数: ${data.trade_count || 0}</p>
            </div>
        `;
    }

    async refreshData() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.textContent = '刷新中...';
        }

        try {
            await this.loadInitialData();
            this.showAlert('数据刷新成功', 'success');
        } catch (error) {
            this.showAlert('数据刷新失败', 'danger');
        } finally {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.textContent = '刷新数据';
            }
        }
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadSystemStatus();
            this.loadTradeHistory();
        }, this.refreshInterval);
    }

    resizeCharts() {
        // 图表大小调整逻辑
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    formatNumber(num) {
        if (typeof num !== 'number') return '0';
        return num.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    formatDate(timestamp) {
        if (!timestamp) return '-';
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        if (!alertContainer) return;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;

        alertContainer.appendChild(alert);

        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
}

// 页面加载完成后初始化仪表板
document.addEventListener('DOMContentLoaded', () => {
    new ArbitrageDashboard();
});

// 导出类以供其他模块使用
window.ArbitrageDashboard = ArbitrageDashboard;
